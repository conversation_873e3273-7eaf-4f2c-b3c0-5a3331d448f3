<?php
// mysql config
$servername = 'mariadb';
$username = 'root';
$password = getenv('DB_PASSWORD');
$dbname = "watchman_maindb";

$options = array(PDO::MYSQL_ATTR_INIT_COMMAND => 'SET NAMES utf8mb4 COLLATE utf8mb4_general_ci');

try {
    // Create a new mysqli connection
    $conn = mysqli_connect($servername, $username, $password, $dbname);

    // Check mysqli connection
    if (!$conn) {
        die("Connection failed: " . mysqli_connect_error());
    } else {
        mysqli_query($conn, 'SET @OLD_CHARACTER_SET_CLIENT="utf8" ');
        mysqli_query($conn, 'SET @OLD_CHARACTER_SET_RESULTS="utf8"');
        mysqli_query($conn, 'SET @OLD_COLLATION_CONNECTION="utf8"');
        mysqli_query($conn, 'SET character_set_results="utf8" ');
        mysqli_query($conn, 'SET character_set_client="utf8" ');
        mysqli_query($conn, 'SET character_set_connection="utf8" ');
    }

    // Create a new PDO instance
    $pdo = new PDO("mysql:host=$servername;dbname=$dbname", $username, $password, $options);

    // Set PDO error mode to exception
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // Set character set for the connection
    $pdo->exec("SET NAMES utf8");

    // You can now use both $conn (mysqli connection) and $pdo (PDO connection) in your code

} catch (PDOException $e) {
    // If there's an error, display the error message
    die("Connection failed: " . $e->getMessage());
}


$global_thai_month = array(
    '',
    'มกราคม',
    'กุมภาพันธ์',
    'มีนาคม',
    'เมษายน',
    'พฤษภาคม',
    'มิถุนายน',
    'กรกฎาคม',
    'สิงหาคม',
    'กันยายน',
    'ตุลาคม',
    'พฤศจิกายน',
    'ธันวาคม'
);

$global_thaimonth = array("ม.ค.", "ก.พ.", "มี.ค.", "เม.ย.", "พ.ค.", "มิ.ย.", "ก.ค.", "ส.ค.", "ก.ย.", "ต.ค.", "พ.ย.", "ธ.ค.");
$global_month = array("01", "02", "03", "04", "05", "06", "07", "08", "09", "10", "11", "12");

// แปลงข้อความเวลา เปนปีไทย
function DateThai($strDate)
{
    global $global_thaimonth;

    if (empty($strDate)) {
        return ''; // ป้องกัน null input
    }

    $timestamp = strtotime($strDate);
    if ($timestamp === false) {
        return ''; // ป้องกัน datetime ผิด format
    }

    $strYear = date("Y", $timestamp) + 543;
    $strMonth = date("n", $timestamp) - 1;
    $strDay = date("j", $timestamp);
    $strHour = date("H:i", $timestamp);
    $strMonthThai = $global_thaimonth[$strMonth] ?? '';

    return "$strDay $strMonthThai $strYear เวลา $strHour";
}


// แปลงข้อความเวลา เปนปีไทย ทั้งวันที่ และเวลา
function DateTimeThai($strDate)
{
    global $global_thaimonth;
    $strYear = date("Y", strtotime($strDate)) + 543;
    $strMonth = date("n", strtotime($strDate)) - 1;
    $strDay = date("j", strtotime($strDate));
    $strHour = date("H", strtotime($strDate));
    $strMinute = date("i", strtotime($strDate));
    //$strSeconds= date("s",strtotime($strDate));
    //$strMonthCut = array("ม.ค.","ก.พ.","มี.ค.","เม.ย.","พ.ค.","มิ.ย.","ก.ค.","ส.ค.","ก.ย.","ต.ค.","พ.ย.","ธ.ค.");
    $strMonthThai = $global_thaimonth[$strMonth];
    return "$strDay $strMonthThai $strYear $strHour:$strMinute";
}

// แปลงข้อความเวลา เปนเวลา
function TimeThai($strDate)
{
    global $global_thaimonth;
    $strYear = date("Y", strtotime($strDate)) + 543;
    $strMonth = date("n", strtotime($strDate)) - 1;
    $strDay = date("j", strtotime($strDate));
    $strHour = date("H", strtotime($strDate));
    $strMinute = date("i", strtotime($strDate));
    //$strSeconds= date("s",strtotime($strDate));
    //$strMonthCut = array("ม.ค.","ก.พ.","มี.ค.","เม.ย.","พ.ค.","มิ.ย.","ก.ค.","ส.ค.","ก.ย.","ต.ค.","พ.ย.","ธ.ค.");
    $strMonthThai = $global_thaimonth[$strMonth];
    return "$strHour:$strMinute";
}

// แปลงข้อความเวลา เปนปีไทย
/*function DateThai2($strDate2)
{
    global $global_thaimonth;
    $strYear = date("Y",strtotime($strDate))+543;
    $strMonth= date("n",strtotime($strDate))-1;
    $strDay= date("j",strtotime($strDate));
    $strHour= date("H",strtotime($strDate));
    //$strMinute= date("i",strtotime($strDate));
    //$strSeconds= date("s",strtotime($strDate));
    //$strMonthCut = array("ม.ค.","ก.พ.","มี.ค.","เม.ย.","พ.ค.","มิ.ย.","ก.ค.","ส.ค.","ก.ย.","ต.ค.","พ.ย.","ธ.ค.");
    $strMonthThai= $global_thaimonth[$strMonth];
    return "$strDay $strMonthThai $strYear";
}*/

// แปลงข้อความเวลา เปนปีไทย เดือน แบบเต็ม
function DateThaiFull($strDate)
{
    global $global_thai_month;
    $strYear = date("Y", strtotime($strDate)) + 543;
    $strMonth = date("n", strtotime($strDate));
    $strDay = date("j", strtotime($strDate));
    $strHour = date("H", strtotime($strDate));
    //$strMinute= date("i",strtotime($strDate));
    //$strSeconds= date("s",strtotime($strDate));
    //$strMonthCut = array("ม.ค.","ก.พ.","มี.ค.","เม.ย.","พ.ค.","มิ.ย.","ก.ค.","ส.ค.","ก.ย.","ต.ค.","พ.ย.","ธ.ค.");
    $strMonthThai = $global_thai_month[$strMonth];
    return "$strDay $strMonthThai $strYear";
}

function DateThaiFull2($strDate)
{
    global $global_thai_month;
    $strYear = date("Y", strtotime($strDate)) + 543;
    $strMonth = date("n", strtotime($strDate));
    $strDay = date("j", strtotime($strDate));
    $strHour = date("H", strtotime($strDate));
    //$strMinute= date("i",strtotime($strDate));
    //$strSeconds= date("s",strtotime($strDate));
    //$strMonthCut = array("ม.ค.","ก.พ.","มี.ค.","เม.ย.","พ.ค.","มิ.ย.","ก.ค.","ส.ค.","ก.ย.","ต.ค.","พ.ย.","ธ.ค.");
    $strMonthThai = $global_thai_month[$strMonth];
    return "วันที่ &nbsp;&nbsp;  $strDay  &nbsp;&nbsp;&nbsp;&nbsp;  เดือน $strMonthThai  &nbsp;&nbsp;&nbsp;&nbsp; พ.ศ. $strYear";
}
// 14 กุมภาพันธ์ 2518
// >> 1975-02-14
function thai_date_2_eng($dates)
{
    global $global_thai_month;
    //ลบ -
    while (strpos($dates, "-") > 0) {
        $dates = trim(str_replace('-', '', $dates));
    }
    //ลบ ช่องว่างเกิน
    $dates = str_replace('  ', ' ', $dates);

    $thais = explode(' ', trim($dates));  //14 กุมภาพันธ์ 2518 >> 14,กุมภาพันธ์,2518
    if (count($thais) >= 3) {
        $tm = $thais[1];
        $eng_m = 0;
        for ($i = 0; $i < count($global_thai_month); $i++) {

            $th_month = $global_thai_month[$i];
            if (strcmp($th_month, $tm) == 0) {
                $eng_m = $i;
                break;
            }
        }
        if ($eng_m > 0) {
            $date = ($thais[2] - 543) . '-' . $eng_m . '-' . $thais[0];
            return $date;
        }
    } elseif (count($thais) == 1) {
        $year = ($thais[0] - 543);
        if ($year > 0) {
            $date = $year . "-01-01";
            return $date;
        }
    }
    // error invalid thai format
    return 0;
}

// 2566-02-14
// >> 1975-02-14
function thai_year_2_eng($dates)
{
    global $global_month;
    //ลบ -
    while (strpos($dates, "-") > 0) {
        $dates = trim(str_replace('-', '', $dates));
    }
    //ลบ ช่องว่างเกิน
    $dates = str_replace('  ', ' ', $dates);

    $thais = explode(' ', trim($dates));  //14 กุมภาพันธ์ 2518 >> 14,กุมภาพันธ์,2518
    if (count($thais) >= 3) {
        $tm = $thais[1];
        $eng_m = 0;
        for ($i = 0; $i < count($global_month); $i++) {

            $th_month = $global_month[$i];
            if (strcmp($th_month, $tm) == 0) {
                $eng_m = $i;
                break;
            }
        }
        if ($eng_m > 0) {
            $date = ($thais[2] - 543) . '-' . $eng_m . '-' . $thais[0];
            return $date;
        }
    } elseif (count($thais) == 1) {
        $year = ($thais[0] - 543);
        if ($year > 0) {
            $date = $year . "-01-01";
            return $date;
        }
    }
    // error invalid thai format
    return 0;
}


// get age from Thai dates
function get_personal_age($birthday)
{
    $dates = thai_date_2_eng($birthday);

    if ($dates) {
        $date1 = new DateTime($dates);
        $date2 = new DateTime("now");
        $diff = $date2->diff($date1);
        return $diff->y;
    }
    return 0;
}

// get age from standard dates
function get_personal_age_2($birthday)
{
    //echo "$birthday <br>";
    if ($birthday && ($birthday != NULL)) {
        $date1 = new DateTime($birthday);
        $date2 = new DateTime("now");
        $diff = $date2->diff($date1);
        return $diff->y;
    }
    return 0;
}

// ฟังก์ชันคำนวณอายุจากวันเกิด กรณีเดือน กุมภาพันธ์ มี 29 วัน
function get_personal_age_29($birthday)
{
    if ($birthday && ($birthday != NULL)) {
        $date1 = new DateTime($birthday);
        $yearNow = date("Y");

        // ตรวจสอบว่าเป็นวันเกิด 29 กุมภาพันธ์ หรือไม่
        if ($date1->format("m-d") == "02-29") {
            // ตรวจสอบว่า ปีปัจจุบันเป็นปีอธิกสุรทินหรือไม่
            if (!checkdate(2, 29, $yearNow)) {
                // ถ้าปีปัจจุบันไม่มี 29 กุมภาพันธ์ ให้ใช้วันที่ 28 กุมภาพันธ์แทน
                $date1 = new DateTime($yearNow . "-02-28");
            }
        }

        $date2 = new DateTime("now");
        $diff = $date2->diff($date1);
        return $diff->y;
    }
    return 0;
}

/*function get_personal_age_2($birthday)
{
    // แสดงค่าที่ได้รับเพื่อเช็คความถูกต้อง
    echo "วันเกิด: $birthday <br>";

    if($birthday && ($birthday != NULL))
    {
        $date1 = new DateTime($birthday);
        $date2 = new DateTime("now");  
        $diff = $date2->diff($date1);
        return $diff->y;
    }
    return 0;
}*/



function compact_array($rows)
{
    $ret = "";
    foreach ($rows as $field => $value) {
        if (gettype($field) == 'string') {
            if (is_array($value)) {
                $value = implode(',', $value); // <-- แก้ตรงนี้
            }
            $ret .= "$field=$value, ";
        }
    }
    return $ret;
}


// Add this function to log page views
function logPageView($acc, $url)
{
    global $conn; // Assuming $conn is your database connection object
    // Get user's IP address
    $ip_address = $_SERVER['REMOTE_ADDR'];
    // กำหนด TimeZone ให้ตรงกับประเทศไทย
    $timestamp = '';
    $timezone = new DateTimeZone('Asia/Bangkok');
    $datetime = new DateTime($timestamp);
    $datetime->setTimezone($timezone);
    $timestamp = $datetime->format('Y-m-d H:i:s');
    $url = mysqli_real_escape_string($conn, $url); // Escape the data to prevent SQL injection

    $sql_ip = "INSERT INTO wm_tb_user_page_view (at_cl_user, at_cl_date, at_cl_url, at_cl_ipaddress) VALUES ('$acc', '$timestamp', '$url', '$ip_address')";
    mysqli_query($conn, $sql_ip);
}

// Add this function to log activity
function log_activity($acc, $action, $data, $ip_address = null)
{
    global $conn; // Assuming $conn is your database connection object
    // Get user's IP address
    // กำหนด TimeZone ให้ตรงกับประเทศไทย
    $timestamp = '';
    $timezone = new DateTimeZone('Asia/Bangkok');
    $datetime = new DateTime($timestamp);
    $datetime->setTimezone($timezone);
    $timestamp = $datetime->format('Y-m-d H:i:s');

    $ip_address = $_SERVER['REMOTE_ADDR'];

    // Ensure your database connection is set to use UTF-8
    mysqli_query($conn, "SET NAMES 'utf8'");
    //$timestamp = date('Y-m-d H:i:s');
    $data = mysqli_real_escape_string($conn, $data); // Escape the data to prevent SQL injection

    $sql3 = "INSERT INTO wm_tb_user_action (at_cl_user, at_cl_date, at_cl_action, at_cl_input, at_cl_ipaddress) VALUES ('$acc', '$timestamp', '$action', '$data', '$ip_address')";
    mysqli_query($conn, $sql3);
}

// Add this function to log activity
function logActivity($acc, $action, $data)
{
    global $conn; // Assuming $conn is your database connection object
    // Get user's IP address
    // กำหนด TimeZone ให้ตรงกับประเทศไทย
    $timestamp = '';
    $timezone = new DateTimeZone('Asia/Bangkok');
    $datetime = new DateTime($timestamp);
    $datetime->setTimezone($timezone);
    $timestamp = $datetime->format('Y-m-d H:i:s');

    $ip_address = $_SERVER['REMOTE_ADDR'];

    // Ensure your database connection is set to use UTF-8
    mysqli_query($conn, "SET NAMES 'utf8'");
    //$timestamp = date('Y-m-d H:i:s');
    $data = mysqli_real_escape_string($conn, $data); // Escape the data to prevent SQL injection

    $sql4 = "INSERT INTO wm_tb_user_action (at_cl_user, at_cl_date, at_cl_action, at_cl_input, at_cl_ipaddress) VALUES ('$acc', '$timestamp', '$action', '$data', '$ip_address')";
    mysqli_query($conn, $sql4);
}

// ฟังก์ชันสำหรับลบช่องว่างและเครื่องหมายคอมม่า ก่อนบันทึก Lat Lon
function sanitizeCoordinate($coordinate)
{
    return str_replace([',', ' '], '', $coordinate);
}