<?php
/**
 * Test file for API to check api_create_person.php functionality
 * After fixing Duplicate Entry Error
 */

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>API Create Person Test</h1>";

// Test data
$testData = [
    'type' => '1',
    'personalType' => '1',
    'idcard' => '5172325439151', // ID Card number that had duplicate issue
    'titlename' => 'นาย',
    'firstname' => 'Test',
    'lastname' => 'System',
    'sex' => 'ชาย',
    'birthday' => '1990-01-01',
    'deathStatus' => '1',
    'userRecorder' => 'Test User',
    'userPosition' => 'Developer'
];

echo "<h2>Test Data:</h2>";
echo "<pre>";
print_r($testData);
echo "</pre>";

// Test duplicate checking function
require_once("api_function.php");

echo "<h2>Test Results:</h2>";

// Test duplicate ID Card checking
echo "<h3>1. Test Duplicate Data Checking:</h3>";
if (function_exists('CheckDuplicateIdCard')) {
    $isDuplicate = CheckDuplicateIdCard($testData['idcard']);
    echo "ID Card: " . $testData['idcard'] . "<br>";
    echo "Check Result: " . ($isDuplicate ? "Duplicate found" : "No duplicate found") . "<br>";
} else {
    echo "CheckDuplicateIdCard function not found<br>";
}

// Test ID Card validation
echo "<h3>2. Test ID Card Validation:</h3>";
$isValidId = ValidateIdentificationNumber($testData['idcard']);
echo "ID Card: " . $testData['idcard'] . "<br>";
echo "Validation Result: " . ($isValidId ? "Valid" : "Invalid") . "<br>";

// Test data validation
echo "<h3>3. Test Complete Data Validation:</h3>";
$inputVals = [
    "id" => NULL,
    "type" => $testData['type'],
    "personType" => $testData['personalType'],
    "crimesType" => NULL,
    "idcard" => $testData['idcard'],
    "passport" => NULL,
    "titlename" => $testData['titlename'],
    "firstname" => $testData['firstname'],
    "lastname" => $testData['lastname'],
    "nickname" => NULL,
    "sex" => $testData['sex'],
    "birthdayTH" => NULL,
    "birthdayEN" => $testData['birthday'],
    "age" => NULL,
    "fatherName" => NULL,
    "fatherIdcard" => NULL,
    "motherName" => NULL,
    "motherIdcard" => NULL,
    "maritalStatus" => NULL,
    "deathStatus" => $testData['deathStatus'],
    "deathDate" => NULL,
    "policeRegion" => NULL,
    "policeProvincial" => NULL,
    "policeStation" => NULL,
    "userRecorder" => $testData['userRecorder'],
    "userPosition" => $testData['userPosition'],
    "recordDate" => date('Y-m-d H:i:s'),
    "imagePath" => NULL
];

$isValidData = ValidationData($inputVals);
echo "Data Validation Result: " . ($isValidData ? "Valid" : "Invalid") . "<br>";

echo "<h3>4. Test Summary:</h3>";
echo "<ul>";
echo "<li>✅ Fixed Syntax Error in api_function.php</li>";
echo "<li>✅ Added CheckDuplicateIdCard function</li>";
echo "<li>✅ Improved Error Handling with English messages</li>";
echo "<li>✅ Added duplicate checking in api_create_person.php</li>";
echo "</ul>";

echo "<h3>5. Usage Instructions:</h3>";
echo "<p>Now the API will:</p>";
echo "<ul>";
echo "<li>Check for duplicate data before adding new records</li>";
echo "<li>Display clear error messages in English</li>";
echo "<li>Handle exceptions properly</li>";
echo "<li>Prevent Fatal Errors from Duplicate Entry</li>";
echo "</ul>";

?>
