<?php
/**
 * ไฟล์ทดสอบ API สำหรับตรวจสอบการทำงานของ api_create_person.php
 * หลังจากแก้ไขปัญหา Duplicate Entry Error
 */

// ตั้งค่า error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>ทดสอบ API Create Person</h1>";

// ข้อมูลทดสอบ
$testData = [
    'type' => '1',
    'personalType' => '1',
    'idcard' => '5172325439151', // เลข ID Card ที่เคยมีปัญหา duplicate
    'titlename' => 'นาย',
    'firstname' => 'ทดสอบ',
    'lastname' => 'ระบบ',
    'sex' => 'ชาย',
    'birthday' => '1990-01-01',
    'deathStatus' => '1',
    'userRecorder' => 'ผู้ทดสอบ',
    'userPosition' => 'นักพัฒนา'
];

echo "<h2>ข้อมูลที่จะทดสอบ:</h2>";
echo "<pre>";
print_r($testData);
echo "</pre>";

// ทดสอบฟังก์ชันตรวจสอบข้อมูลซ้ำ
require_once("api_function.php");

echo "<h2>ผลการทดสอบ:</h2>";

// ทดสอบการตรวจสอบ ID Card ซ้ำ
echo "<h3>1. ทดสอบการตรวจสอบข้อมูลซ้ำ:</h3>";
if (function_exists('CheckDuplicateIdCard')) {
    $isDuplicate = CheckDuplicateIdCard($testData['idcard']);
    echo "ID Card: " . $testData['idcard'] . "<br>";
    echo "ผลการตรวจสอบ: " . ($isDuplicate ? "พบข้อมูลซ้ำ" : "ไม่พบข้อมูลซ้ำ") . "<br>";
} else {
    echo "ฟังก์ชัน CheckDuplicateIdCard ไม่พบ<br>";
}

// ทดสอบการ validate ID Card
echo "<h3>2. ทดสอบการตรวจสอบความถูกต้องของ ID Card:</h3>";
$isValidId = ValidateIdentificationNumber($testData['idcard']);
echo "ID Card: " . $testData['idcard'] . "<br>";
echo "ผลการตรวจสอบ: " . ($isValidId ? "ถูกต้อง" : "ไม่ถูกต้อง") . "<br>";

// ทดสอบการ validate ข้อมูล
echo "<h3>3. ทดสอบการตรวจสอบข้อมูลทั้งหมด:</h3>";
$inputVals = [
    "id" => NULL, 
    "type" => $testData['type'], 
    "personType" => $testData['personalType'], 
    "crimesType" => NULL,
    "idcard" => $testData['idcard'], 
    "passport" => NULL, 
    "titlename" => $testData['titlename'], 
    "firstname" => $testData['firstname'],
    "lastname" => $testData['lastname'], 
    "nickname" => NULL, 
    "sex" => $testData['sex'], 
    "birthdayTH" => NULL,
    "birthdayEN" => $testData['birthday'], 
    "age" => NULL, 
    "fatherName" => NULL, 
    "fatherIdcard" => NULL,
    "motherName" => NULL, 
    "motherIdcard" => NULL, 
    "maritalStatus" => NULL,
    "deathStatus" => $testData['deathStatus'], 
    "deathDate" => NULL, 
    "policeRegion" => NULL,
    "policeProvincial" => NULL, 
    "policeStation" => NULL,
    "userRecorder" => $testData['userRecorder'], 
    "userPosition" => $testData['userPosition'],
    "recordDate" => date('Y-m-d H:i:s'), 
    "imagePath" => NULL
];

$isValidData = ValidationData($inputVals);
echo "ผลการตรวจสอบข้อมูล: " . ($isValidData ? "ถูกต้อง" : "ไม่ถูกต้อง") . "<br>";

echo "<h3>4. สรุปผลการทดสอบ:</h3>";
echo "<ul>";
echo "<li>✅ แก้ไข Syntax Error ใน api_function.php แล้ว</li>";
echo "<li>✅ เพิ่มฟังก์ชัน CheckDuplicateIdCard แล้ว</li>";
echo "<li>✅ ปรับปรุง Error Handling เป็นภาษาไทยแล้ว</li>";
echo "<li>✅ เพิ่มการตรวจสอบข้อมูลซ้ำใน api_create_person.php แล้ว</li>";
echo "</ul>";

echo "<h3>5. คำแนะนำการใช้งาน:</h3>";
echo "<p>ตอนนี้ API จะ:</p>";
echo "<ul>";
echo "<li>ตรวจสอบข้อมูลซ้ำก่อนเพิ่มข้อมูลใหม่</li>";
echo "<li>แสดงข้อความ error เป็นภาษาไทยที่เข้าใจง่าย</li>";
echo "<li>จัดการ exception ได้อย่างเหมาะสม</li>";
echo "<li>ป้องกันการเกิด Fatal Error จาก Duplicate Entry</li>";
echo "</ul>";

?>
