<?php
/**
 * Test file for ID validation functions
 * Tests various ID formats to ensure proper validation
 */

require_once("api_function.php");

echo "<h1>ID Validation Test</h1>";

// Test cases
$testCases = [
    // Valid Thai IDs
    '*********0123' => 'Should be INVALID (wrong checksum)',
    '*********0122' => 'Should be INVALID (wrong checksum)', 
    '1101700207354' => 'Should be VALID (if checksum is correct)',
    
    // Valid Registration Numbers
    'A1234567' => 'Should be VALID (registration)',
    'AB*********' => 'Should be VALID (registration)',
    'ABC*********0' => 'Should be VALID (registration)',
    
    // Valid Passport Numbers
    'A1234567' => 'Should be VALID (passport format)',
    'AB*********' => 'Should be VALID (passport format)',
    '12345678' => 'Should be VALID (8-digit passport)',
    '*********' => 'Should be VALID (9-digit passport)',
    
    // Invalid cases
    '*********012' => 'Should be INVALID (12 digits, not Thai ID)',
    '*********01' => 'Should be INVALID (11 digits, not valid format)',
    '*********0' => 'Should be INVALID (10 digits, not valid format)',
    '1234567' => 'Should be INVALID (7 digits, too short)',
    'ABCD123456' => 'Should be INVALID (too many letters)',
    '' => 'Should be INVALID (empty)',
    '   ' => 'Should be INVALID (whitespace only)',
];

echo "<h2>Test Results:</h2>";
echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th>ID Number</th><th>Expected</th><th>Valid?</th><th>Type</th><th>Result</th></tr>";

foreach ($testCases as $idNumber => $expected) {
    $isValid = ValidateIdentificationNumber($idNumber);
    $type = GetIdentificationNumberType($idNumber);
    $result = $isValid ? "✅ VALID" : "❌ INVALID";
    $color = $isValid ? "lightgreen" : "lightcoral";
    
    echo "<tr style='background-color: {$color};'>";
    echo "<td><strong>{$idNumber}</strong></td>";
    echo "<td>{$expected}</td>";
    echo "<td>{$result}</td>";
    echo "<td>{$type}</td>";
    echo "<td>" . ($isValid ? "PASS" : "FAIL") . "</td>";
    echo "</tr>";
}

echo "</table>";

// Test specific Thai ID validation
echo "<h2>Thai ID Checksum Test:</h2>";
echo "<p>Testing the problematic ID: <strong>*********0122</strong></p>";

$testId = '*********0122';
$isValidThaiId = ValidateThaiID($testId);
$isValidGeneral = ValidateIdentificationNumber($testId);
$idType = GetIdentificationNumberType($testId);

echo "<ul>";
echo "<li>ValidateThaiID('$testId'): " . ($isValidThaiId ? "✅ Valid" : "❌ Invalid") . "</li>";
echo "<li>ValidateIdentificationNumber('$testId'): " . ($isValidGeneral ? "✅ Valid" : "❌ Invalid") . "</li>";
echo "<li>GetIdentificationNumberType('$testId'): <strong>$idType</strong></li>";
echo "</ul>";

// Calculate correct checksum for demonstration
echo "<h3>Checksum Calculation for *********0122:</h3>";
$id = '*********0122';
$sum = 0;
echo "<p>Calculation: ";
for ($i = 0; $i < 12; $i++) {
    $digit = (int)$id[$i];
    $multiplier = 13 - $i;
    $product = $digit * $multiplier;
    $sum += $product;
    echo "({$digit} × {$multiplier}) + ";
}
echo " = {$sum}</p>";

$checkDigit = (11 - ($sum % 11)) % 10;
$actualCheckDigit = (int)$id[12];

echo "<p>Expected check digit: <strong>{$checkDigit}</strong></p>";
echo "<p>Actual check digit: <strong>{$actualCheckDigit}</strong></p>";
echo "<p>Match: " . ($checkDigit == $actualCheckDigit ? "✅ Yes" : "❌ No") . "</p>";

// Suggest a valid Thai ID
$correctId = substr($id, 0, 12) . $checkDigit;
echo "<p>Corrected ID would be: <strong>{$correctId}</strong></p>";

echo "<h2>Summary:</h2>";
echo "<p>The ID <strong>*********0122</strong> is now properly rejected because:</p>";
echo "<ul>";
echo "<li>It has 13 digits (looks like Thai ID)</li>";
echo "<li>But fails the checksum validation</li>";
echo "<li>System no longer accepts invalid 13-digit numbers as other ID types</li>";
echo "</ul>";

?>
