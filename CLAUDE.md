# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a **PHP REST API** for the "Watchman" person management system - a law enforcement/police database application for managing person records. The API handles CRUD operations for person records with Thai ID validation, passport numbers, and police station associations.

## Development Setup

### Requirements
- PHP 7.4+ with MySQL/MySQLi extension
- MySQL database server
- Web server (Apache/Nginx) with PHP support
- `../Condb.php` - Database connection file (located in parent directory)
- `../users.inc.php` - User authentication file (located in parent directory)
- `../Alert.php` - Alert messaging system (located in parent directory)

### Local Development
Since this is a PHP API without build tools:
1. Ensure PHP is installed and web server is running
2. Place files in web-accessible directory
3. Configure database connection in `../Condb.php`
4. Test endpoints using tools like Postman or curl

## API Architecture

### Core Files
- `api_function.php` - Core business logic and utility functions
- `api_access.php` - Authentication endpoint
- `api_create_person.php` - Create new person records
- `api_get_person.php` - Retrieve person details
- `api_list_person.php` - List/search persons with pagination
- `api_update_person.php` - Update existing person records  
- `api_delete_person.php` - Delete person records
- `api_options.php` - Dropdown options and police station data

### Key Functions (api_function.php)
- **ValidateThaiID()** - Validates 13-digit Thai national ID cards using checksum
- **ValidateIdentificationNumber()** - Supports Thai ID, passport, and registration numbers
- **PersonManagement()** - Main CRUD operation handler with MySQL stored procedures
- **ListPersons()** - Paginated person listing with search
- **VerifyToken()** - External API token verification via curl
- **formatThaiDate()** - Thai Buddhist calendar date formatting
- **ValidationData()** - Comprehensive data validation with conditional field requirements

### Data Models
Person records include:
- Basic info: Thai ID, passport, name, title, sex, birth date
- Family info: father/mother names and IDs
- Location: police region/provincial/station hierarchy
- Status: death status, marital status, person/crime types
- Files: photo uploads stored in `/uploaded/Image_PS/`

### Database Integration
- Uses MySQL stored procedures via MySQLi:
  - `Person_Management()` for CRUD operations
  - `Person_List()` for paginated search results
  - `Person_GetDetails()` for individual records
  - `Dropdown_ListOptions()` and `Dropdown_ListPoliceStation()` for form options
- All database calls use prepared statements for security

### Authentication System
- Token-based authentication via external API (aliza.k-lynx.com)
- `VerifyToken()` validates bearer tokens and returns username
- Session management for user login state

### File Handling
- Photo uploads: JPEG format, stored as `ID_[idcard]_[timestamp].jpg`
- File paths converted to full URLs using `$_SERVER["HTTP_HOST"]`
- Old files automatically deleted on updates

## API Endpoints

- `POST api_create_person.php` - Create person
- `GET api_get_person.php` - Get person (by id or idcard)
- `GET api_list_person.php` - List persons with search/pagination
- `POST api_update_person.php` - Update person
- `POST api_delete_person.php` - Delete person
- `GET api_options.php` - Get dropdown options
- `GET api_access.php` - Authentication

## Key Business Rules

### ID Validation
- Thai ID: 13-digit with mathematical checksum validation
- Passport: Alphanumeric patterns (A-Z + 4-10 digits, etc.)
- Registration numbers: Various alphanumeric formats

### Police Station Hierarchy
- Region → Provincial → Station validation via `CheckPoliceStation()`
- All three levels must be valid and properly linked

### Person Types
- Type 1: General person (`personType` required)
- Type 2: Crime-related person (`crimesType` required)

### Death Status
- Status 1: Alive
- Status 2: Deceased (requires `deathDate`)