<?php
/**
 * <PERSON><PERSON>t to identify and optionally clean up invalid ID numbers in the database
 * This script will find records with invalid Thai ID checksums
 */

require_once("api_function.php");

echo "<h1>Database ID Validation Cleanup</h1>";

// First, let's check if the problematic record exists
echo "<h2>Checking for Invalid Records:</h2>";

$testId = '*********0122';
echo "<h3>Searching for ID: {$testId}</h3>";

// Use the existing API to check
$searchResult = GetPersonDetails([$testId, 'idcard']);

echo "<h4>Search Result:</h4>";
echo "<pre>";
print_r($searchResult);
echo "</pre>";

if ($searchResult['status'] === true) {
    echo "<div style='background-color: #ffcccc; padding: 10px; border: 1px solid #ff0000;'>";
    echo "<h4>⚠️ FOUND INVALID RECORD!</h4>";
    echo "<p>Record with invalid ID <strong>{$testId}</strong> exists in database.</p>";
    echo "<p>Details:</p>";
    echo "<ul>";
    echo "<li>ID: " . $searchResult['details']['id'] . "</li>";
    echo "<li>Name: " . $searchResult['details']['fullname'] . "</li>";
    echo "<li>ID Card: " . $searchResult['details']['idcard'] . "</li>";
    echo "</ul>";
    echo "</div>";
    
    // Validate the ID
    $isValidThaiId = ValidateThaiID($testId);
    $isValidGeneral = ValidateIdentificationNumber($testId);
    
    echo "<h4>Validation Results:</h4>";
    echo "<ul>";
    echo "<li>Thai ID Validation: " . ($isValidThaiId ? "✅ Valid" : "❌ Invalid") . "</li>";
    echo "<li>General ID Validation: " . ($isValidGeneral ? "✅ Valid" : "❌ Invalid") . "</li>";
    echo "</ul>";
    
    if (!$isValidThaiId && !$isValidGeneral) {
        echo "<div style='background-color: #ffffcc; padding: 10px; border: 1px solid #ffaa00;'>";
        echo "<h4>🔧 RECOMMENDED ACTION:</h4>";
        echo "<p>This record should be deleted because:</p>";
        echo "<ul>";
        echo "<li>The ID number has 13 digits (appears to be Thai ID)</li>";
        echo "<li>But it fails the Thai ID checksum validation</li>";
        echo "<li>It's not a valid registration or passport number format</li>";
        echo "</ul>";
        
        echo "<h5>To delete this record:</h5>";
        echo "<p>You can use the delete API:</p>";
        echo "<code>POST api_delete_person.php</code><br>";
        echo "<code>Body: {\"idcard\": \"{$testId}\"}</code>";
        echo "</div>";
    }
} else {
    echo "<div style='background-color: #ccffcc; padding: 10px; border: 1px solid #00aa00;'>";
    echo "<h4>✅ GOOD NEWS!</h4>";
    echo "<p>No record found with ID <strong>{$testId}</strong>.</p>";
    echo "<p>Either:</p>";
    echo "<ul>";
    echo "<li>The record was never actually created, or</li>";
    echo "<li>It has already been cleaned up</li>";
    echo "</ul>";
    echo "</div>";
}

// Test the current validation
echo "<h2>Current Validation Test:</h2>";
echo "<h3>Testing ID: {$testId}</h3>";

$isValid = ValidateIdentificationNumber($testId);
$idType = GetIdentificationNumberType($testId);

echo "<table border='1' style='border-collapse: collapse;'>";
echo "<tr><th>Test</th><th>Result</th></tr>";
echo "<tr><td>ValidateIdentificationNumber()</td><td>" . ($isValid ? "✅ Valid" : "❌ Invalid") . "</td></tr>";
echo "<tr><td>GetIdentificationNumberType()</td><td><strong>{$idType}</strong></td></tr>";
echo "</table>";

echo "<h2>Prevention Measures:</h2>";
echo "<div style='background-color: #e6f3ff; padding: 10px; border: 1px solid #0066cc;'>";
echo "<h4>✅ IMPLEMENTED FIXES:</h4>";
echo "<ul>";
echo "<li><strong>Enhanced Validation:</strong> 13-digit numbers must pass Thai ID checksum</li>";
echo "<li><strong>Strict Type Checking:</strong> Invalid 13-digit numbers are rejected</li>";
echo "<li><strong>Clear Error Messages:</strong> Users get specific feedback about invalid IDs</li>";
echo "<li><strong>Duplicate Prevention:</strong> System checks for existing records before creation</li>";
echo "</ul>";
echo "</div>";

echo "<h2>Testing Other ID Formats:</h2>";
$validExamples = [
    'A1234567' => 'Registration/Passport',
    'AB*********' => 'Registration/Passport', 
    '12345678' => '8-digit Passport',
    '*********' => '9-digit Passport'
];

echo "<table border='1' style='border-collapse: collapse;'>";
echo "<tr><th>ID Number</th><th>Type</th><th>Valid?</th></tr>";

foreach ($validExamples as $id => $description) {
    $isValid = ValidateIdentificationNumber($id);
    $type = GetIdentificationNumberType($id);
    $color = $isValid ? 'lightgreen' : 'lightcoral';
    
    echo "<tr style='background-color: {$color};'>";
    echo "<td><strong>{$id}</strong></td>";
    echo "<td>{$description}</td>";
    echo "<td>" . ($isValid ? "✅ Valid" : "❌ Invalid") . " ({$type})</td>";
    echo "</tr>";
}

echo "</table>";

echo "<h2>Next Steps:</h2>";
echo "<ol>";
echo "<li><strong>Test the API:</strong> Try creating a person with ID <code>*********0122</code> - should now fail</li>";
echo "<li><strong>Clean up existing data:</strong> If invalid records exist, delete them using the API</li>";
echo "<li><strong>Monitor logs:</strong> Watch for any other invalid IDs being submitted</li>";
echo "<li><strong>User education:</strong> Inform users about proper ID format requirements</li>";
echo "</ol>";

?>
